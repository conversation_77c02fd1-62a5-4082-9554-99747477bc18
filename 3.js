// NSURLSession Hook Script - 只显示提交数据和响应数据

if (ObjC.available) {
    try {
        var NSURLSession = ObjC.classes.NSURLSession;
        if (NSURLSession) {

            // Hook dataTaskWithRequest:completionHandler:
            var dataTaskWithRequest = NSURLSession['- dataTaskWithRequest:completionHandler:'];
            if (dataTaskWithRequest) {
                Interceptor.attach(dataTaskWithRequest.implementation, {
                    onEnter: function(args) {
                        var request = new ObjC.Object(args[2]);
                        var completionHandler = args[3];

                        // 获取请求体数据
                        var body = request.HTTPBody();
                        if (body) {
                            var bodyData = new ObjC.Object(body);
                            try {
                                var bodyString = Memory.readUtf8(bodyData.bytes(), bodyData.length());
                                console.log("\n=== 提交数据 ===");
                                console.log(bodyString);
                            } catch (e) {
                                console.log("\n=== 提交数据 ===");
                                console.log("[二进制数据，长度: " + bodyData.length() + "]");
                            }
                        }

                        // Hook 完成回调来获取响应数据
                        if (completionHandler) {
                            var originalCallback = new ObjC.Block(completionHandler);
                            var newCallback = ObjC.implement(originalCallback, function(data, response, error) {
                                if (data && !error) {
                                    try {
                                        var responseData = new ObjC.Object(data);
                                        var responseString = Memory.readUtf8(responseData.bytes(), responseData.length());
                                        console.log("\n=== 响应数据 ===");
                                        console.log(responseString);
                                    } catch (e) {
                                        console.log("\n=== 响应数据 ===");
                                        console.log("[二进制数据，长度: " + responseData.length() + "]");
                                    }
                                }
                                return originalCallback(data, response, error);
                            });
                            args[3] = newCallback;
                        }
                    }
                });
            }

            // Hook uploadTaskWithRequest:fromData:completionHandler:
            var uploadTask = NSURLSession['- uploadTaskWithRequest:fromData:completionHandler:'];
            if (uploadTask) {
                Interceptor.attach(uploadTask.implementation, {
                    onEnter: function(args) {
                        var data = new ObjC.Object(args[3]);
                        var completionHandler = args[4];

                        // 显示上传数据
                        try {
                            var uploadString = Memory.readUtf8(data.bytes(), data.length());
                            console.log("\n=== 上传数据 ===");
                            console.log(uploadString);
                        } catch (e) {
                            console.log("\n=== 上传数据 ===");
                            console.log("[二进制数据，长度: " + data.length() + "]");
                        }

                        // Hook 完成回调来获取响应数据
                        if (completionHandler) {
                            var originalCallback = new ObjC.Block(completionHandler);
                            var newCallback = ObjC.implement(originalCallback, function(data, response, error) {
                                if (data && !error) {
                                    try {
                                        var responseData = new ObjC.Object(data);
                                        var responseString = Memory.readUtf8(responseData.bytes(), responseData.length());
                                        console.log("\n=== 响应数据 ===");
                                        console.log(responseString);
                                    } catch (e) {
                                        console.log("\n=== 响应数据 ===");
                                        console.log("[二进制数据，长度: " + responseData.length() + "]");
                                    }
                                }
                                return originalCallback(data, response, error);
                            });
                            args[4] = newCallback;
                        }
                    }
                });
            }
        }

    } catch (error) {
        console.log("Hook 失败: " + error);
    }
}