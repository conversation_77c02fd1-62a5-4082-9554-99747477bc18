// NSURLSession Hook Script
// Hook NSURLSession 网络请求

console.log("[*] 开始 Hook NSURLSession...");

// Hook NSURLSessionDataTask
if (ObjC.available) {
    try {
        // Hook NSURLSession dataTaskWithRequest:completionHandler:
        var NSURLSession = ObjC.classes.NSURLSession;
        if (NSURLSession) {
            console.log("[+] 找到 NSURLSession 类");

            // Hook dataTaskWithRequest:completionHandler:
            var dataTaskWithRequest = NSURLSession['- dataTaskWithRequest:completionHandler:'];
            if (dataTaskWithRequest) {
                Interceptor.attach(dataTaskWithRequest.implementation, {
                    onEnter: function(args) {
                        var request = new ObjC.Object(args[2]);
                        var url = request.URL().absoluteString().toString();
                        var method = request.HTTPMethod().toString();

                        console.log("\n[NSURLSession] 数据任务创建");
                        console.log("URL: " + url);
                        console.log("Method: " + method);

                        // 获取请求头
                        var headers = request.allHTTPHeaderFields();
                        if (headers) {
                            console.log("Headers: " + headers.toString());
                        }

                        // 获取请求体
                        var body = request.HTTPBody();
                        if (body) {
                            var bodyData = new ObjC.Object(body);
                            console.log("Body Length: " + bodyData.length());

                            // 尝试转换为字符串
                            try {
                                var bodyString = Memory.readUtf8(bodyData.bytes(), Math.min(bodyData.length(), 1024));
                                console.log("Body: " + bodyString);
                            } catch (e) {
                                console.log("Body: [二进制数据]");
                            }
                        }
                    },
                    onLeave: function(retval) {
                        console.log("[NSURLSession] 数据任务已创建");
                    }
                });
            }

            // Hook dataTaskWithURL:completionHandler:
            var dataTaskWithURL = NSURLSession['- dataTaskWithURL:completionHandler:'];
            if (dataTaskWithURL) {
                Interceptor.attach(dataTaskWithURL.implementation, {
                    onEnter: function(args) {
                        var url = new ObjC.Object(args[2]);
                        console.log("\n[NSURLSession] URL数据任务创建");
                        console.log("URL: " + url.absoluteString().toString());
                    }
                });
            }

            // Hook uploadTaskWithRequest:fromData:completionHandler:
            var uploadTask = NSURLSession['- uploadTaskWithRequest:fromData:completionHandler:'];
            if (uploadTask) {
                Interceptor.attach(uploadTask.implementation, {
                    onEnter: function(args) {
                        var request = new ObjC.Object(args[2]);
                        var data = new ObjC.Object(args[3]);
                        var url = request.URL().absoluteString().toString();

                        console.log("\n[NSURLSession] 上传任务创建");
                        console.log("URL: " + url);
                        console.log("Upload Data Length: " + data.length());
                    }
                });
            }

            // Hook downloadTaskWithRequest:completionHandler:
            var downloadTask = NSURLSession['- downloadTaskWithRequest:completionHandler:'];
            if (downloadTask) {
                Interceptor.attach(downloadTask.implementation, {
                    onEnter: function(args) {
                        var request = new ObjC.Object(args[2]);
                        var url = request.URL().absoluteString().toString();

                        console.log("\n[NSURLSession] 下载任务创建");
                        console.log("URL: " + url);
                    }
                });
            }
        }

        // Hook NSURLSessionTask resume 方法
        var NSURLSessionTask = ObjC.classes.NSURLSessionTask;
        if (NSURLSessionTask) {
            var resume = NSURLSessionTask['- resume'];
            if (resume) {
                Interceptor.attach(resume.implementation, {
                    onEnter: function(args) {
                        var task = new ObjC.Object(args[0]);
                        var request = task.originalRequest();
                        if (request) {
                            var url = request.URL().absoluteString().toString();
                            console.log("\n[NSURLSessionTask] 任务开始执行");
                            console.log("URL: " + url);
                        }
                    }
                });
            }
        }

        console.log("[+] NSURLSession Hook 设置完成");

    } catch (error) {
        console.log("[-] Hook NSURLSession 失败: " + error);
    }
} else {
    console.log("[-] Objective-C 运行时不可用");
}